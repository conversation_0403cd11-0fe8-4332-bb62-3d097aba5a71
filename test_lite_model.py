#!/usr/bin/env python3
"""
Test script for LITE model to verify basic functionality.
This script tests the model components without requiring full datasets or LLaMA token.
"""

import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import replace_mask_with_values

def test_replace_mask_function():
    """Test the replace_mask_with_values function"""
    print("Testing replace_mask_with_values function...")
    
    # Test case 1: Single mask
    sentence1 = "The temperature is [MASK] degrees."
    values1 = torch.tensor([25.5])
    result1 = replace_mask_with_values(sentence1, values1)
    print(f"Input: {sentence1}")
    print(f"Values: {values1}")
    print(f"Output: {result1}")
    print()
    
    # Test case 2: Multiple masks
    sentence2 = "Temperature: [MASK], Humidity: [MASK], Pressure: [MASK]"
    values2 = torch.tensor([22.3, 65.8, 1013.2])
    result2 = replace_mask_with_values(sentence2, values2)
    print(f"Input: {sentence2}")
    print(f"Values: {values2}")
    print(f"Output: {result2}")
    print()
    
    return True

def test_moe_component():
    """Test the MoE (Mixture of Experts) component"""
    print("Testing MoE component...")
    
    try:
        from model.LITE import MoE_ffn, MLP
        
        # Test MLP
        mlp = MLP(d_model=768, dropout_rate=0.3)
        test_input = torch.randn(5, 768)  # 5 samples, 768 features
        mlp_output = mlp(test_input)
        print(f"MLP input shape: {test_input.shape}")
        print(f"MLP output shape: {mlp_output.shape}")
        print(f"MLP output sample: {mlp_output[:3].flatten()}")
        print()
        
        # Test MoE
        moe = MoE_ffn(num_experts=8, hidden_dim=768, dropout_rate=0.3, top_k=2)
        moe_output = moe(test_input)
        print(f"MoE input shape: {test_input.shape}")
        print(f"MoE output shape: {moe_output.shape}")
        print(f"MoE output sample: {moe_output[:3].flatten()}")
        print()
        
        return True
        
    except Exception as e:
        print(f"Error testing MoE component: {e}")
        return False

def test_model_components_without_llama():
    """Test model components that don't require LLaMA"""
    print("Testing model components (without LLaMA)...")
    
    try:
        # Test if we can import the model classes
        from model.LITE import Model, MoE_ffn, MLP
        print("✓ Successfully imported LITE model classes")
        
        # Test MoE component
        moe_success = test_moe_component()
        if moe_success:
            print("✓ MoE component test passed")
        else:
            print("✗ MoE component test failed")
            
        return moe_success
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def create_dummy_data():
    """Create dummy data for testing"""
    print("Creating dummy data for testing...")
    
    # Create dummy image (3x224x224 RGB image)
    dummy_image = torch.randn(1, 3, 224, 224)
    
    # Create dummy text input (tokenized)
    dummy_input_ids = torch.randint(0, 1000, (1, 50))  # batch_size=1, seq_len=50
    dummy_attention_mask = torch.ones(1, 50)
    
    # Create dummy week and month sentences
    dummy_week_sentences = [["Week sentence " + str(i) for i in range(6)]]
    dummy_month_sentences = [["Month sentence " + str(i) for i in range(11)]]
    
    # Create dummy stats
    dummy_stats = torch.randn(1, 30)  # batch_size=1, 30 time steps
    
    print(f"Dummy image shape: {dummy_image.shape}")
    print(f"Dummy input_ids shape: {dummy_input_ids.shape}")
    print(f"Dummy attention_mask shape: {dummy_attention_mask.shape}")
    print(f"Dummy week sentences: {len(dummy_week_sentences[0])} sentences")
    print(f"Dummy month sentences: {len(dummy_month_sentences[0])} sentences")
    print(f"Dummy stats shape: {dummy_stats.shape}")
    print()
    
    return {
        'pixel_values': dummy_image,
        'input_ids': dummy_input_ids,
        'attention_mask': dummy_attention_mask,
        'week_sentences': dummy_week_sentences,
        'month_sentences': dummy_month_sentences,
        'stats': dummy_stats
    }

def main():
    """Main test function"""
    print("=" * 60)
    print("LITE Model Verification Test")
    print("=" * 60)
    print()
    
    # Test 1: Replace mask function
    print("Test 1: replace_mask_with_values function")
    print("-" * 40)
    mask_test_success = test_replace_mask_function()
    print(f"Result: {'PASSED' if mask_test_success else 'FAILED'}")
    print()
    
    # Test 2: Model components
    print("Test 2: Model components (without LLaMA)")
    print("-" * 40)
    component_test_success = test_model_components_without_llama()
    print(f"Result: {'PASSED' if component_test_success else 'FAILED'}")
    print()
    
    # Test 3: Dummy data creation
    print("Test 3: Dummy data creation")
    print("-" * 40)
    try:
        dummy_data = create_dummy_data()
        data_test_success = True
        print("Result: PASSED")
    except Exception as e:
        print(f"Error creating dummy data: {e}")
        data_test_success = False
        print("Result: FAILED")
    print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"replace_mask_with_values: {'PASSED' if mask_test_success else 'FAILED'}")
    print(f"Model components: {'PASSED' if component_test_success else 'FAILED'}")
    print(f"Dummy data creation: {'PASSED' if data_test_success else 'FAILED'}")
    print()
    
    all_tests_passed = mask_test_success and component_test_success and data_test_success
    print(f"Overall result: {'ALL TESTS PASSED' if all_tests_passed else 'SOME TESTS FAILED'}")
    
    if not all_tests_passed:
        print("\nNote: Full model testing requires:")
        print("- LLaMA token for the LLM decoder")
        print("- Proper dataset with images and text")
        print("- GPU with sufficient memory")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
